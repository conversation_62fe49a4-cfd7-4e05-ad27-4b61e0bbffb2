# 告警管理模块部署指南

## 概述

告警管理模块的数据库结构已从统一的DDL文件中抽取出来，形成独立的模块化数据库结构。本文档说明如何在不同环境中部署告警模块的数据库结构。

## 文件结构

```
nta_3.0/services/alarm/
├── src/main/resources/sql/
│   ├── alarm-schema.sql          # 告警模块完整DDL
│   └── README.md                 # 数据库结构说明
├── DEPLOYMENT.md                 # 本文件
└── ...
```

## 部署方式

### 1. 独立部署（推荐）

#### 开发环境
```bash
# 直接执行告警模块DDL
psql -h localhost -U postgres -d nta -f src/main/resources/sql/alarm-schema.sql
```

#### 生产环境
```bash
# 使用环境变量
psql -h $DB_HOST -U $DB_USER -d $DB_NAME -f src/main/resources/sql/alarm-schema.sql
```

### 2. Helm部署集成

#### 方案A：独立Job（推荐）

创建专门的告警模块数据库初始化Job：

```yaml
# templates/alarm-db-init-job.yaml
apiVersion: batch/v1
kind: Job
metadata:
  name: {{ include "nta.fullname" . }}-alarm-db-init
  namespace: {{ .Values.global.namespace }}
  annotations:
    "helm.sh/hook": pre-install,pre-upgrade
    "helm.sh/hook-weight": "1"
    "helm.sh/hook-delete-policy": before-hook-creation,hook-succeeded
spec:
  template:
    spec:
      restartPolicy: Never
      containers:
      - name: alarm-db-init
        image: postgres:15
        env:
        - name: PGHOST
          value: {{ .Values.postgresql.host }}
        - name: PGUSER
          value: {{ .Values.postgresql.username }}
        - name: PGPASSWORD
          valueFrom:
            secretKeyRef:
              name: {{ .Values.postgresql.secretName }}
              key: password
        - name: PGDATABASE
          value: {{ .Values.postgresql.database }}
        command:
        - /bin/bash
        - -c
        - |
          echo "Initializing alarm module database..."
          psql -f /sql/alarm-schema.sql
          echo "Alarm module database initialization completed."
        volumeMounts:
        - name: alarm-schema
          mountPath: /sql
      volumes:
      - name: alarm-schema
        configMap:
          name: {{ include "nta.fullname" . }}-alarm-schema
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "nta.fullname" . }}-alarm-schema
  namespace: {{ .Values.global.namespace }}
data:
  alarm-schema.sql: |
{{ .Files.Get "services/alarm/src/main/resources/sql/alarm-schema.sql" | indent 4 }}
```

#### 方案B：集成到现有初始化流程

修改现有的数据库初始化Job，添加告警模块DDL：

```yaml
# 在现有的db-init-job中添加
- name: alarm-schema
  configMap:
    name: {{ include "nta.fullname" . }}-alarm-schema
```

### 3. Docker Compose部署

```yaml
# docker-compose.yml
version: '3.8'
services:
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: nta
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    volumes:
      - ./services/alarm/src/main/resources/sql/alarm-schema.sql:/docker-entrypoint-initdb.d/02-alarm-schema.sql
    ports:
      - "5432:5432"
```

## 配置验证

### 1. 数据库连接验证

```bash
# 验证告警模块表是否创建成功
psql -h $DB_HOST -U $DB_USER -d $DB_NAME -c "
SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'public' 
  AND table_name LIKE 'alarm%' 
ORDER BY table_name;
"
```

预期输出应包含：
```
alarm
alarm_attacker
alarm_extended_target
alarm_output_config
alarm_reason
alarm_records
alarm_rules
alarm_source
alarm_statistics
alarm_subscription
alarm_targets
alarm_type_config
alarm_types
alarm_victim
alarm_whitelist
```

### 2. 应用配置验证

确认告警服务的数据源配置正确：

```yaml
# application.yml
spring:
  datasource:
    dynamic:
      primary: nta-postgres
      datasource:
        nta-postgres:
          url: jdbc:postgresql://${DB_HOST}:${DB_PORT}/${DB_NAME}
          username: ${DB_USER}
          password: ${DB_PASSWORD}
          driver-class-name: org.postgresql.Driver
```

### 3. 实体映射验证

确认实体类的表映射正确：
- `AlarmType` → `alarm_type_config`
- `Alarm` → `alarm`
- `AlarmSource` → `alarm_source`
- 等等...

## 迁移策略

### 从统一DDL迁移

如果当前使用统一的DDL文件，迁移步骤：

1. **备份现有数据**
```bash
pg_dump -h $DB_HOST -U $DB_USER -d $DB_NAME -t 'alarm*' > alarm_backup.sql
```

2. **验证独立DDL**
```bash
# 在测试环境验证
psql -h test_host -U test_user -d test_db -f alarm-schema.sql
```

3. **更新部署配置**
- 添加告警模块DDL的ConfigMap
- 创建独立的初始化Job
- 更新Helm values

4. **部署验证**
```bash
helm upgrade --install nta ./helm-chart --dry-run
```

## 故障排除

### 常见问题

1. **表已存在错误**
   - 检查是否有重复的DDL执行
   - 使用`DROP TABLE IF EXISTS`确保幂等性

2. **外键约束错误**
   - 确保表的创建顺序正确
   - 检查引用的表是否存在

3. **权限错误**
   - 确认数据库用户有CREATE TABLE权限
   - 检查数据库连接配置

### 调试命令

```bash
# 检查表结构
\d+ alarm

# 检查索引
\di alarm*

# 检查外键约束
SELECT conname, conrelid::regclass, confrelid::regclass 
FROM pg_constraint 
WHERE contype = 'f' AND conrelid::regclass::text LIKE 'alarm%';
```

## 最佳实践

1. **版本控制**：将DDL文件纳入版本控制
2. **环境隔离**：不同环境使用不同的数据库实例
3. **备份策略**：定期备份告警相关数据
4. **监控**：监控数据库性能和告警表的增长
5. **文档维护**：及时更新数据库结构文档

## 联系方式

如有问题，请联系：
- 告警模块开发团队
- 数据库管理员
- DevOps团队
