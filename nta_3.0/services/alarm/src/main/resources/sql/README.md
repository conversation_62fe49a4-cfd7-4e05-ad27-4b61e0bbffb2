# 告警管理模块数据库结构

## 概述

本目录包含告警管理模块的独立数据库结构定义，从原本的统一DDL文件中抽取出来，便于模块化管理和维护。

## 文件说明

### `alarm-schema.sql`
告警管理模块的完整数据库结构定义，包含：

#### 核心业务表
- `alarm` - 告警主表（聚合根）
- `alarm_source` - 告警源信息表
- `alarm_attacker` - 告警攻击者表
- `alarm_victim` - 告警受害者表
- `alarm_targets` - 告警目标表
- `alarm_reason` - 告警原因表

#### 配置和管理表
- `alarm_type_config` - 告警类型配置表（对应AlarmType实体）
- `alarm_rules` - 告警规则表
- `alarm_records` - 告警记录表（业务主存储）
- `alarm_subscription` - 告警订阅表

#### 辅助表
- `alarm_whitelist` - 告警白名单表
- `alarm_output_config` - 告警输出配置表
- `alarm_extended_target` - 告警扩展目标表
- `tb_tag_info` - 标签信息表
- `tb_download_task` - 下载任务表

#### 关联查询支持表
- `knowledge_type_vo` - 知识类型视图表
- `label` - 标签表
- `feature_rule` - 特征规则表

#### 兼容性表
- `alarm_statistics` - 告警统计表
- `threat_knowledge_alarm` - 威胁知识告警表
- `alarm_types` - 告警类型定义表（知识库用）

## 使用方法

### 1. 独立部署
```bash
# 直接执行告警模块的DDL
psql -h <host> -U <username> -d <database> -f alarm-schema.sql
```

### 2. 集成到现有部署流程
在Helm部署中，可以将此文件作为独立的ConfigMap挂载：

```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: alarm-schema-config
data:
  alarm-schema.sql: |
    {{ .Files.Get "services/alarm/src/main/resources/sql/alarm-schema.sql" | nindent 4 }}
```

### 3. 开发环境初始化
```bash
# 在开发环境中初始化告警模块数据库
docker exec -i postgres_container psql -U postgres -d nta < alarm-schema.sql
```

## 表关系说明

### 核心关系
```
alarm (1) ←→ (1) alarm_source
alarm (1) ←→ (n) alarm_attacker
alarm (1) ←→ (n) alarm_victim
alarm (1) ←→ (n) alarm_targets
alarm (1) ←→ (n) alarm_reason
```

### 业务关系
```
alarm_rules (1) ←→ (n) alarm_records
alarm_subscription (1) ←→ (n) notification_logs
```

## 实体映射

| 实体类 | 对应表 | 说明 |
|--------|--------|------|
| `Alarm` | `alarm` | 告警聚合根 |
| `AlarmSource` | `alarm_source` | 告警源信息 |
| `AlarmAttacker` | `alarm_attacker` | 攻击者信息 |
| `AlarmVictim` | `alarm_victim` | 受害者信息 |
| `AlarmTargets` | `alarm_targets` | 目标信息 |
| `AlarmReason` | `alarm_reason` | 告警原因 |
| `AlarmType` | `alarm_type_config` | 告警类型配置 |
| `AnalysisLabelInfoEntity` | `tb_tag_info` | 标签信息 |
| `DownloadTask` | `tb_download_task` | 下载任务 |

## 注意事项

### 1. 表名冲突解决
- `alarm_type_config` vs `alarm_types`：前者用于系统配置，后者用于知识库
- 实体类`AlarmType`映射到`alarm_type_config`表

### 2. 数据库兼容性
- 使用PostgreSQL特有的数据类型（如JSONB、INET、数组）
- 包含完整的外键约束和级联删除
- 自动更新时间戳触发器

### 3. 索引优化
- 为常用查询字段创建了索引
- 支持高效的分页查询和条件过滤

## 维护指南

### 1. 添加新表
在相应的章节中添加新表定义，并更新本README文档。

### 2. 修改现有表
使用ALTER TABLE语句，并考虑向后兼容性。

### 3. 数据迁移
如需数据迁移，请创建单独的迁移脚本。

## 版本历史

- v1.0.0 (2025-01-22): 初始版本，从统一DDL中抽取告警模块相关表结构
